import { FC, SetStateAction, useState, useRef, useEffect } from 'react'
import styles from './phishing-campaign-detail-page.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignDetailPageProps } from './phishing-campaign-detail-page.d'
import { Breadcrumbs, ButtonIcon, Tooltip } from '@/shared/ui'
import SettingsBoldIcon from '@/shared/ui/Icon/icons/components/SettingsBoldIcon'
import LightningIcon from '@/shared/ui/Icon/icons/components/LightningIcon'
import EmailingIcon from '@/shared/ui/Icon/icons/components/EmailingIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { usePhishingCampaignDetail } from './hooks/usePhishingCampaignDetail'
import { PhishingCampaignDetailStatistics } from './compose/PhishingCampaignDetailStatistics'
import { PhishingCampaignTemplateCards } from './compose/PhishingCampaignTemplateCards'
import { PhishingCampaignDetailDeleteModal } from './compose/PhishingCampaignDetailDeleteModal'
import { PhishingCampaignIncidentCard } from '@/shared/components'
import { useParams } from 'react-router-dom'
import PhishingCampaignCourse from '../phishing-campaign-course'
import { useTranslation } from 'react-i18next'
import { phishingMutations } from '@/entities/phishing'
import { PhishingCampaignRedirectPageCard } from './components/phishing-campaign-redirect-page-card/phishing-campaign-redirect-page-card'
import { PhishingCampaignNotification } from './components/phishing-campaign-notification'
import { PhishingCampaignPost } from './components/phishing-campaign-post'

const cx = classNamesBind.bind(styles)

const REFETCH_DELAY = 1000
const STOP_DELAY_DURATION = 4000

type PhishingCampaignDetailStopProps = {
  status?: 'completed' | string
  refetch: () => Promise<unknown>
  setStopDelay: React.Dispatch<SetStateAction<boolean>>
}

const PhishingCampaignDetailStop = ({
  status,
  refetch,
  setStopDelay,
}: PhishingCampaignDetailStopProps) => {
  const { campaign_id = '' } = useParams()

  const [completeTrigger, { isLoading }] =
    phishingMutations.useCompletePhishingCampaignByIdMutation()
  const { t } = useTranslation()
  const [visible, setVisible] = useState(true)

  const timeoutRefs = useRef<NodeJS.Timeout[]>([])

  useEffect(() => {
    return () => {
      timeoutRefs.current.forEach(clearTimeout)
      timeoutRefs.current = []
    }
  }, [])

  if (status === 'completed' || !visible) return null

  const onStop = async () => {
    try {
      await completeTrigger(campaign_id).unwrap()
      setVisible(false)
      setStopDelay(true)

      const refetchTimeout = setTimeout(() => {
        refetch()
          .then(() => setVisible(true))
          .catch(error => {
            console.error('Error refetching campaign data:', error)
            setVisible(true)
          })
      }, REFETCH_DELAY)

      const stopDelayTimeout = setTimeout(() => {
        setStopDelay(false)
      }, STOP_DELAY_DURATION)

      timeoutRefs.current.push(refetchTimeout, stopDelayTimeout)
    } catch (error) {
      console.error('Error stopping campaign:', error)
    }
  }

  return (
    <Tooltip content={<>{t('commons:stop_mailing')}</>}>
      <ButtonIcon
        disabled={isLoading}
        className={cx('stop__icon')}
        onClick={onStop}
        color={'gray'}
        iconSize='24'
        size='32'
        icon='stopCircle'
      />
    </Tooltip>
  )
}

export const PhishingCampaignDetailPage: FC<PhishingCampaignDetailPageProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()
  const {
    BREADCRUMBS,
    campaignData,
    campaignInternal,
    campaignCourse,
    stopDelay,
    templatesData,
    isTemplatesLoading,
    setStopDelay,
    refetchCourse,
  } = usePhishingCampaignDetail()

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title__wrapper')}>
        <Breadcrumbs items={BREADCRUMBS} />
        <div className={cx('title__box')}>
          {campaignData?.is_testing && (
            <IconWrapper size='32' color='primary'>
              <SettingsBoldIcon />
            </IconWrapper>
          )}
          {campaignData?.is_autophish && (
            <IconWrapper size='32' color='primary'>
              <LightningIcon />
            </IconWrapper>
          )}
          {campaignData && !campaignData?.is_autophish && !campaignData?.is_testing && (
            <IconWrapper size='32' color='primary'>
              <EmailingIcon />
            </IconWrapper>
          )}
          <h1 className={cx('title')} title={campaignData?.name || ''}>
            {campaignData?.name || ''}
          </h1>
          <div className={cx('actions__wrapper')}>
            <PhishingCampaignDetailStop
              status={campaignData?.status}
              refetch={refetchCourse}
              setStopDelay={setStopDelay}
            />
            <PhishingCampaignDetailDeleteModal />
          </div>
        </div>
      </div>
      <PhishingCampaignIncidentCard infoType='date' data={campaignData} />
      <PhishingCampaignDetailStatistics data={campaignData} />
      <h2 className={cx('templates__title')}>{t('commons:templates')}</h2>
      <PhishingCampaignTemplateCards
        data={templatesData}
        isLoading={isTemplatesLoading}
        campaignInternal={campaignInternal}
      />
      <PhishingCampaignRedirectPageCard
        campaignInternal={campaignInternal}
        campaignStatus={campaignData?.status}
        templates={templatesData}
      />
      <PhishingCampaignNotification
        campaignInternal={campaignInternal}
        campaignStatus={campaignData?.status}
      />
      <PhishingCampaignPost
        campaignInternal={campaignInternal}
        campaignStatus={campaignData?.status}
        stopDelay={stopDelay}
      />
      <PhishingCampaignCourse campaignCourse={campaignCourse} campaignData={campaignData} />
    </div>
  )
}

export default PhishingCampaignDetailPage
